package com.iflytek.lynxiao.support.service.elastic.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.support.config.SupportProperties;
import com.iflytek.lynxiao.support.consts.RegionCode;
import com.iflytek.lynxiao.support.consts.StatisticsModule;
import com.iflytek.lynxiao.support.consts.OprationEnum;
import com.iflytek.lynxiao.support.dto.MailStatisticsConditionDTO;
import com.iflytek.lynxiao.support.service.elastic.ElasticSearchService;
import com.iflytek.lynxiao.support.util.StatisticsUtil;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregation;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.TermsValuesSourceBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class ElasticSearchServiceImpl implements ElasticSearchService {


    @Autowired(required = false)
    @Qualifier("hfLogRestHighLevelClient")
    private RestHighLevelClient hfLogRestHighLevelClient;

    @Autowired(required = false)
    @Qualifier("shLogRestHighLevelClient")
    private RestHighLevelClient shLogRestHighLevelClient;

    @Autowired(required = false)
    @Qualifier("dxLogRestHighLevelClient")
    private RestHighLevelClient dxLogRestHighLevelClient;

    @Value("${lynxiao.flow.analysis.health.scene.appIds}")
    private String healthSceneAppIds;

    @Resource
    private StatisticsUtil statisticsUtil;

    @Value("${lynxiao.env-code:prod}")
    private String envCode;

    private final SupportProperties supportProperties;

    Map<String, RestHighLevelClient> restHighLevelClientHashMap = new HashMap<>();

    public ElasticSearchServiceImpl(SupportProperties supportProperties) {
        this.supportProperties = supportProperties;
    }

    @PostConstruct
    public void init() {
        restHighLevelClientHashMap.put(RegionCode.HF, hfLogRestHighLevelClient);
        restHighLevelClientHashMap.put(RegionCode.SH, shLogRestHighLevelClient);
        restHighLevelClientHashMap.put(RegionCode.DX, dxLogRestHighLevelClient);
    }

    @Override
    public List<QueryBuilder> recursionCondition(String jsonStr) {
        try {

            List<MailStatisticsConditionDTO> params = JSON.parseArray(jsonStr, MailStatisticsConditionDTO.class);
            if (CollectionUtil.isEmpty(params)) {
                return new ArrayList<>();
            }
            List<QueryBuilder> queryBuilders = new ArrayList<>();
            for (MailStatisticsConditionDTO param : params) {
                switch (param.getOperate()) {
                    case "EQ":
                        queryBuilders.add(QueryBuilders.termQuery(param.getFiled(), param.getValue()));
                        break;
                    case "IN":
                        queryBuilders.add(QueryBuilders.termsQuery(param.getFiled(), Arrays.asList(param.getValue().split(","))));
                        break;
                    case "GT":
                    case "GTE":
                    case "LT":
                    case "LTE":
                        queryBuilders.add(QueryBuilders.existsQuery(param.getFiled()));
                        int value = Integer.parseInt(param.getValue());
                        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(param.getFiled());
                        if (OprationEnum.GT.getCode().equals(param.getOperate())) {
                            rangeQueryBuilder.gt(value);
                        } else if (OprationEnum.GTE.getCode().equals(param.getOperate())) {
                            rangeQueryBuilder.gte(value);
                        } else if (OprationEnum.LT.getCode().equals(param.getOperate())) {
                            rangeQueryBuilder.lt(value);
                        } else if (OprationEnum.LTE.getCode().equals(param.getOperate())) {
                            rangeQueryBuilder.lte(value);
                        }
                        queryBuilders.add(rangeQueryBuilder);
                        break;
                    case "CONT":
                        queryBuilders.add(QueryBuilders.matchPhraseQuery(param.getFiled(), param.getValue()));
                        break;
                    case "NO_RESULT":
                        // 创建一个 bool 查询
                        queryBuilders.add(QueryBuilders.termQuery(param.getFiled(), 0));
                        break;
                    default:
                        // Handle unknown operation or log a warning.
                        break;
                }
            }

            return queryBuilders;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("解析参数串: {}", jsonStr);
            return new ArrayList<>();
        }
    }

    @Override
    public long countDocuments(String region, String indexName, List<QueryBuilder> queries) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();


        // 动态添加查询条件
        for (QueryBuilder query : queries) {
            boolQueryBuilder.must(query);
        }


        // 设置查询条件
        searchSourceBuilder.query(boolQueryBuilder);

        CountRequest countRequest = new CountRequest(indexName);
        countRequest.source(searchSourceBuilder);

        if (log.isTraceEnabled()) {
            log.trace("region:{}, indexName:{},  CountRequest: {}", region, indexName, countRequest.source().toString());
        }

        try {
            CountResponse countResponse = restHighLevelClientHashMap.get(region).count(countRequest, RequestOptions.DEFAULT);
            return countResponse.getCount();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return 0;
    }

    @Override
    public long queryTotalNum(String region, String indexName, String module, String bizCode) {
        List<MailStatisticsConditionDTO> flowAnalysisCondition = getFlowAnalysisCondition(region, bizCode, module);
        List<QueryBuilder> queryBuilders = recursionCondition(JSON.toJSONString(flowAnalysisCondition));
        return countDocuments(region, indexName, queryBuilders);
    }

    @Override
    public List<JSONObject> getDocuments(String region, String indexName, List<QueryBuilder> queries, Integer limit) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        for (QueryBuilder query : queries) {
            boolQueryBuilder.must(query);
        }

        searchSourceBuilder.query(boolQueryBuilder);

        searchSourceBuilder.from(0);
        searchSourceBuilder.size(limit);

        SearchRequest searchRequest = new SearchRequest(indexName);
        searchRequest.source(searchSourceBuilder);

        try {
            SearchResponse searchResponse = restHighLevelClientHashMap.get(region).search(searchRequest, RequestOptions.DEFAULT);

            // 处理搜索结果
            List<JSONObject> documents = new ArrayList<>();
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                JSONObject document = new JSONObject();
                document = JSONObject.parseObject(hit.getSourceAsString(), JSONObject.class);
                documents.add(document);
            }

            return documents;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return new ArrayList<>();
    }

    @Override
    public List<String> aggregationQuery(String region, String indexName, String field) {
        return aggregationQuery(region, indexName, field, Collections.emptyMap());
    }

    @Override
    public List<String> aggregationQuery(String regionCode, String indexName, String field, Map<String, String> filters) {
        String formattedField = "%s.keyword".formatted(field);
        List<String> result = new ArrayList<>();

        // Pagination variables
        Map<String, Object> afterKey = null;
        int pageSize = 1000; // Number of results per page

        do {
            // Create search request
            SearchRequest searchRequest = new SearchRequest(indexName);
            searchRequest.source().size(0); // No document hits, only aggregation results

            // Build composite aggregation
            CompositeAggregationBuilder aggregationBuilder = AggregationBuilders.composite("aggregationBurial",
                            Collections.singletonList(new TermsValuesSourceBuilder("field").field(formattedField)))
                    .size(pageSize); // Set the size for the composite aggregation

            if (afterKey != null) {
                aggregationBuilder.aggregateAfter(afterKey);
            }

            // Build query
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery("type.keyword", "SearchAPI-Response"))
                    .filter(QueryBuilders.termQuery("labels.env.keyword", supportProperties.getEnv()));

            if (filters != null && !filters.isEmpty()) {
                for (Map.Entry<String, String> entry : filters.entrySet()) {
                    if (StringUtils.isNotBlank(entry.getValue())) {
                        queryBuilder.filter(QueryBuilders.termQuery(entry.getKey() + ".keyword", entry.getValue()));
                    }
                }
            }


            searchRequest.source()
                    .query(queryBuilder)
                    .aggregation(aggregationBuilder);

            try {
                // Execute search
                SearchResponse searchResponse = restHighLevelClientHashMap.get(regionCode).search(searchRequest, RequestOptions.DEFAULT);

                // Extract aggregation results
                CompositeAggregation compositeAggregation = searchResponse.getAggregations().get("aggregationBurial");
                afterKey = compositeAggregation.afterKey();

                for (CompositeAggregation.Bucket bucket : compositeAggregation.getBuckets()) {
                    EsAggsBucketValue esAggsBucketValue = getEsAggsBucketValue(bucket);
                    if (esAggsBucketValue == null) continue;

                    String value = esAggsBucketValue.getField();
                    if (value.contains(",")) {
                        result.addAll(Arrays.asList(value.split(",")));
                    } else {
                        result.add(value);
                    }
                }
            } catch (Exception e) {
                log.error("Error during aggregation query: {}", e.getMessage(), e);
                break;
            }
        } while (afterKey != null); // Continue until no more pages

        // Remove duplicates and return results
        return result.stream().distinct().collect(Collectors.toList());
    }

    @Nullable
    private static EsAggsBucketValue getEsAggsBucketValue(CompositeAggregation.Bucket bucket) {
        Map<String, Object> bucketKeyMap = bucket.getKey();
        if (bucketKeyMap == null || bucketKeyMap.isEmpty()) {
            log.warn("解析聚合结果bucket异常: {}", bucket.getKeyAsString());
            return null;
        }
        EsAggsBucketValue esAggsBucketValue = new JSONObject(bucketKeyMap).toJavaObject(EsAggsBucketValue.class);
        if (esAggsBucketValue == null || StringUtils.isBlank(esAggsBucketValue.getField()) || "null".equals(esAggsBucketValue.getField())) {
            log.warn("解析聚合结果bucket异常: {}", bucket.getKeyAsString());
            return null;
        }
        return esAggsBucketValue;
    }

    @Override
    public Long aggregationGetRequestCount(String region, String indexName, String field, Map<String, String> filters) {
        String formattedField = "%s.keyword".formatted(field);
        long result = 0L;

        // Pagination variables
        Map<String, Object> afterKey = null;
        int pageSize = 1000; // Number of results per page

        do {
            // Create search request
            SearchRequest searchRequest = new SearchRequest(indexName);
            searchRequest.source().size(0); // No document hits, only aggregation results

            // Build composite aggregation
            CompositeAggregationBuilder aggregationBuilder = AggregationBuilders.composite("aggregationBurial",
                            Collections.singletonList(new TermsValuesSourceBuilder("field").field(formattedField)))
                    .size(pageSize); // Set the size for the composite aggregation

            if (afterKey != null) {
                aggregationBuilder.aggregateAfter(afterKey);
            }

            // Build query
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery("type.keyword", "SearchAPI-Response"))
                    .filter(QueryBuilders.termQuery("labels.env.keyword", supportProperties.getEnv()));

            if (filters != null && !filters.isEmpty()) {
                for (Map.Entry<String, String> entry : filters.entrySet()) {
                    if (StringUtils.isNotBlank(entry.getValue())) {
                        queryBuilder.filter(QueryBuilders.termQuery(entry.getKey() + ".keyword", entry.getValue()));
                    }
                }
            }

            searchRequest.source()
                    .query(queryBuilder)
                    .aggregation(aggregationBuilder);

            try {
                // Execute search
                SearchResponse searchResponse = restHighLevelClientHashMap.get(region).search(searchRequest, RequestOptions.DEFAULT);

                // Extract aggregation results
                CompositeAggregation compositeAggregation = searchResponse.getAggregations().get("aggregationBurial");
                afterKey = compositeAggregation.afterKey();

                for (CompositeAggregation.Bucket bucket : compositeAggregation.getBuckets()) {
                    //出现次数
                    long times = bucket.getDocCount();
                    int docsCount;

                    try {
                        EsAggsBucketValue esAggsBucketValue = getEsAggsBucketValue(bucket);
                        if (esAggsBucketValue == null) continue;

                        docsCount = Integer.parseInt(esAggsBucketValue.getField());
                    } catch (Exception e) {
                        log.warn("获取docsCount时发生异常，可能是非数字类型的值: {}", bucket.getKeyAsString());
                        continue;
                    }

                    if (docsCount >= 0) {
                        result += times * docsCount; // 累加每个桶的文档计数
                    }

                }
            } catch (Exception e) {
                log.error("Error during aggregation query: {}", e.getMessage(), e);
                break;
            }
        } while (afterKey != null); // Continue until no more pages

        // Remove duplicates and return results
        return result;
    }

    @Override
    public List<JSONObject> getAllDocumentsByScrollWithMax(String region, String indexName, List<QueryBuilder> queries, TimeValue keepAlive, Integer pageSize, Integer maxTotalDocs) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        for (QueryBuilder query : queries) {
            boolQueryBuilder.must(query);
        }

        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(pageSize);

        SearchRequest searchRequest = new SearchRequest(indexName);
        searchRequest.source(searchSourceBuilder);
        searchRequest.scroll(keepAlive);

        List<JSONObject> allDocuments = new ArrayList<>();
        String scrollId = null;

        try {
            // 初始查询
            SearchResponse searchResponse = restHighLevelClientHashMap.get(region).search(searchRequest, RequestOptions.DEFAULT);
            scrollId = searchResponse.getScrollId();
            processSearchHits(searchResponse.getHits().getHits(), allDocuments);

            // 检查是否达到maxTotalDocs
            if (maxTotalDocs != null && allDocuments.size() >= maxTotalDocs) {
                allDocuments = allDocuments.subList(0, maxTotalDocs);
                // 清理scroll上下文
                ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
                clearScrollRequest.addScrollId(scrollId);
                restHighLevelClientHashMap.get(region).clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
                return allDocuments;
            }

            // 后续分页查询
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(keepAlive);

            while (true) {
                SearchResponse scrollResponse = restHighLevelClientHashMap.get(region).scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = scrollResponse.getScrollId();
                SearchHit[] hits = scrollResponse.getHits().getHits();

                if (hits.length == 0) {
                    break;
                }

                processSearchHits(hits, allDocuments);

                // 检查是否达到maxTotalDocs
                if (maxTotalDocs != null && allDocuments.size() >= maxTotalDocs) {
                    allDocuments = allDocuments.subList(0, maxTotalDocs);
                    break;
                }
            }

            // 清理scroll上下文
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            restHighLevelClientHashMap.get(region).clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

            return allDocuments;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (scrollId != null) {
                try {
                    ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
                    clearScrollRequest.addScrollId(scrollId);
                    restHighLevelClientHashMap.get(region).clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
                } catch (Exception ex) {
                    log.error("Error clearing scroll context: " + ex.getMessage(), ex);
                }
            }
        }

        return new ArrayList<>();
    }

    private void processSearchHits(SearchHit[] hits, List<JSONObject> allDocuments) {
        log.debug("allDocuments size:  {}", allDocuments.size());
        for (SearchHit hit : hits) {
            JSONObject document = JSONObject.parseObject(hit.getSourceAsString(), JSONObject.class);
            allDocuments.add(document);
        }
    }

    private List<MailStatisticsConditionDTO> getFlowAnalysisCondition(String region, String bizCode, String module) {

        List<MailStatisticsConditionDTO> result = new ArrayList<>();
        // type
        MailStatisticsConditionDTO type = new MailStatisticsConditionDTO("type.keyword", "EQ", "SearchAPI-Response");
        result.add(type);

        // 业务应用概览
        if (StatisticsModule.APP.getCode().equals(module)) {
            MailStatisticsConditionDTO app = new MailStatisticsConditionDTO("appId", "EQ", bizCode);
            result.add(app);
        }
        // 产品方案概览
        if (StatisticsModule.PRO_CODE.getCode().equals(module)) {
            MailStatisticsConditionDTO pvid = new MailStatisticsConditionDTO("pvid", "EQ", bizCode);
            result.add(pvid);
        }
        // 医疗场景标签流量占比统计
        if (StatisticsModule.HEALTH.getCode().equals(module)) {
            MailStatisticsConditionDTO scene = new MailStatisticsConditionDTO("scene", "CONT", bizCode);
            result.add(scene);
            MailStatisticsConditionDTO appId = new MailStatisticsConditionDTO("appId", "IN", healthSceneAppIds);
            result.add(appId);
        }
        return result;
    }

    @Getter
    @Setter
    static class EsAggsBucketValue {
        private String field;
    }
}
