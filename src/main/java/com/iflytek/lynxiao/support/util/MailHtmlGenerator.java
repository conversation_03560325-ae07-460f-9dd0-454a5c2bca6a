package com.iflytek.lynxiao.support.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.iflytek.lynxiao.support.dto.BizEqualIdResultDistribution;
import com.iflytek.lynxiao.support.dto.MailStatisticsDTO;
import com.iflytek.lynxiao.support.dto.statistics.BurialDTO;
import com.iflytek.lynxiao.support.dto.statistics.FlowAnalysisMailDTO;

import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedFlow;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedFlowVersion;
import com.iflytek.lynxiao.support.repository.support.entity.BusinessEqualIdAnalysis;
import lombok.extern.log4j.Log4j2;
import org.jfree.data.category.DefaultCategoryDataset;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 邮件HTML生成工具类
 * 用于生成统计报表的HTML内容
 */
@Log4j2
public class MailHtmlGenerator {

    /**
     * 生成业务唯一标识HTML表格
     */
    public static String generateBizEqualHtml(List<BusinessEqualIdAnalysis> bizEqualIdAnalysisList,
                                              Map<Long, GeneratedFlow> flowMap, String tableName) {
        StringBuilder table = new StringBuilder();
        table.append("<p>").append(escapeHtml(tableName)).append("</p>");
        table.append("基于医疗侧透传的qid，将同一条【原始用户query】在自研搜索多条链路中的搜索返回结果数求和：若总和>0，则计1次有结果；若=0，则计1次无结果；计算得出基于原始用户query统计的有结果率。<br><br>");

        if (CollectionUtil.isEmpty(bizEqualIdAnalysisList)) {
            log.error("generateBizEqualHtml-生成主表格 未获取到详细流量数据!");
            return table.toString();
        }

        // 拼接表格头
        table.append("<table>")
                .append("<tr>")
                .append("<th>日期</th>")
                .append("<th>产品方案</th>")
                .append("<th>原始用户请求总数</th>")
                .append("<th>有结果</th>")
                .append("<th>无结果</th>")
                .append("<th>有结果百分比</th>")
                .append("</tr>");

        // 按日期分组并降序排序
        Map<String, List<BusinessEqualIdAnalysis>> dateGroupMap = bizEqualIdAnalysisList.stream()
                .collect(Collectors.groupingBy(BusinessEqualIdAnalysis::getDateStr));
        Map<String, List<BusinessEqualIdAnalysis>> sortedDateGroupMap = new TreeMap<>(Comparator.reverseOrder());
        sortedDateGroupMap.putAll(dateGroupMap);

        for (Map.Entry<String, List<BusinessEqualIdAnalysis>> dateEntry : sortedDateGroupMap.entrySet()) {
            String date = dateEntry.getKey();
            List<BusinessEqualIdAnalysis> analysisListForDate = dateEntry.getValue();

            // 按产品方案分组
            Map<String, List<BusinessEqualIdAnalysis>> productIdGroupMap = analysisListForDate.stream()
                    .collect(Collectors.groupingBy(BusinessEqualIdAnalysis::getProductId));

            int rowSize = Math.max(productIdGroupMap.size(), 1); // 确保 rowspan 至少为 1
            table.append("<tr>")
                    .append("<td rowspan=\"").append(rowSize).append("\">").append(escapeHtml(date)).append("</td>");

            if (productIdGroupMap.isEmpty()) {
                table.append("<td style=\"text-align: left;\">--</td>")
                        .append("<td>--</td>")
                        .append("<td>--</td>")
                        .append("<td>--</td>")
                        .append("<td>--</td>")
                        .append("</tr>");
            } else {
                boolean isFirstRow = true;
                for (Map.Entry<String, List<BusinessEqualIdAnalysis>> appEntry : productIdGroupMap.entrySet()) {
                    String productId = appEntry.getKey();
                    List<BusinessEqualIdAnalysis> appAnalysisList = appEntry.getValue();

                    // 获取产品方案名称
                    GeneratedFlow product = flowMap.get(Long.valueOf(productId));
                    String productName = product != null ? product.getName() + "(" + product.getCode() + ")" : productId;

                    // 聚合同一个 appId 的所有数据
                    long totalRequestCount = appAnalysisList.stream()
                            .mapToLong(BusinessEqualIdAnalysis::getRequestCount)
                            .sum();
                    long totalHasResult = appAnalysisList.stream()
                            .mapToLong(BusinessEqualIdAnalysis::getHasResult)
                            .sum();
                    long totalNoResult = appAnalysisList.stream()
                            .mapToLong(BusinessEqualIdAnalysis::getNoResult)
                            .sum();

                    // 计算有结果百分比
                    BigDecimal hasResultPercentage = DataCalculateUtil.calculatePercentage(totalHasResult, totalRequestCount);

                    if (!isFirstRow) {
                        table.append("<tr>");
                    }

                    table.append("<td style=\"text-align: left;\">").append(escapeHtml(productName)).append("</td>")
                            .append("<td>").append(DataCalculateUtil.getFormatNumber(totalRequestCount)).append("</td>")
                            .append("<td>").append(DataCalculateUtil.getFormatNumber(totalHasResult)).append("</td>")
                            .append("<td>").append(DataCalculateUtil.getFormatNumber(totalNoResult)).append("</td>")
                            .append("<td>").append(hasResultPercentage).append("%</td>")
                            .append("</tr>");

                    isFirstRow = false;
                }
            }
        }

        table.append("</table><br><br>");
        return table.toString();
    }

    // HTML 转义工具方法
    private static String escapeHtml(String input) {
        if (input == null) return "";
        return input.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#39;");
    }


    /**
     * 生成业务唯一标识召回doc总数分布表格
     */
    public static String generateBizEqualHtml4DocsCountDistribution(List<BusinessEqualIdAnalysis> bizEqualIdAnalysisList,
                                                                    Map<Long, GeneratedFlow> flowMap) {
        String table = "";
        table = table + "基于【原始用户query】，对求和后的返回结果数进行聚合统计，计算不同返回结果数的占比。<br><br>";

        if (CollectionUtil.isEmpty(bizEqualIdAnalysisList)) {
            log.error("generateBizEqualHtml-召回doc数聚合分析 未获取到详细流量数据!");
            return table.toString();
        }

        // 创建表格头
        table = table + "<table>" +
                "    <tr>" +
                "        <th>日期</th>" +
                "        <th>产品方案</th>" +
                "        <th>返回结果数</th>" +
                "        <th>原始用户请求总数</th>" +
                "        <th>流量占比</th>" +
                "    </tr>";

        // 按日期分组，使用TreeMap按日期降序排列
        Map<String, List<BusinessEqualIdAnalysis>> dateGroupMap = bizEqualIdAnalysisList.stream()
                .collect(Collectors.groupingBy(BusinessEqualIdAnalysis::getDateStr));
        Map<String, List<BusinessEqualIdAnalysis>> sortedDateGroupMap = new TreeMap<>(Comparator.reverseOrder());
        sortedDateGroupMap.putAll(dateGroupMap);

        for (Map.Entry<String, List<BusinessEqualIdAnalysis>> dateEntry : sortedDateGroupMap.entrySet()) {
            String date = dateEntry.getKey();
            List<BusinessEqualIdAnalysis> analysisListForDate = dateEntry.getValue();

            // 按appId分组
            Map<String, List<BusinessEqualIdAnalysis>> productIdGroupMap = analysisListForDate.stream()
                    .collect(Collectors.groupingBy(BusinessEqualIdAnalysis::getProductId));

            for (Map.Entry<String, List<BusinessEqualIdAnalysis>> appEntry : productIdGroupMap.entrySet()) {
                String productId = appEntry.getKey();
                List<BusinessEqualIdAnalysis> appAnalysisList = appEntry.getValue();

                // 获取产品方案名
                GeneratedFlow product = flowMap.get(Long.valueOf(productId));
                String productName = product != null ? product.getName() + "(" + product.getCode() + ")" : productId;

                // 计算总请求数（当前分组中所有requestCount的和）
                long totalRequestCount = appAnalysisList.stream()
                        .mapToLong(BusinessEqualIdAnalysis::getRequestCount)
                        .sum();

                // 收集所有分布数据并按docsCountSum分组
                Map<Long, List<BizEqualIdResultDistribution>> docsCountGroupMap = appAnalysisList.stream()
                        .flatMap(analysis -> {
                            List<BizEqualIdResultDistribution> distributions =
                                    JSONArray.parseArray(analysis.getDistribution(), BizEqualIdResultDistribution.class);
                            return distributions.stream();
                        })
                        .collect(Collectors.groupingBy(BizEqualIdResultDistribution::getDocsCountSum));

                // 按docsCountSum排序
                Map<Long, List<BizEqualIdResultDistribution>> sortedDocsCountGroupMap =
                        new TreeMap<>(docsCountGroupMap);

                // 计算每个docsCountSum的行数
                int totalRows = sortedDocsCountGroupMap.size();
                if (totalRows == 0) continue; // 跳过没有分布数据的记录

                boolean isFirstRow = true;

                for (Map.Entry<Long, List<BizEqualIdResultDistribution>> docsCountEntry : sortedDocsCountGroupMap.entrySet()) {
                    Long docsCountSum = docsCountEntry.getKey();
                    List<BizEqualIdResultDistribution> distributions = docsCountEntry.getValue();

                    // 计算相同docsCountSum的requestTotal相加
                    int totalRequestForDocsCount = distributions.stream()
                            .mapToInt(BizEqualIdResultDistribution::getRequestTotal)
                            .sum();

                    // 计算流量占比
                    BigDecimal trafficPercentage = DataCalculateUtil.calculatePercentage(totalRequestForDocsCount, totalRequestCount);

                    // 生成表格行
                    if (isFirstRow) {
                        table = table + "<tr>" +
                                "            <td rowspan=\"" + totalRows + "\">" + date + "</td>" +
                                "            <td rowspan=\"" + totalRows + "\" style=\"text-align: left;\">" + productName + "</td>";
                        isFirstRow = false;
                    } else {
                        table = table + "<tr>";
                    }

                    table = table +
                            "<td>" + DataCalculateUtil.getFormatNumber(docsCountSum) + "</td>" +
                            "<td>" + DataCalculateUtil.getFormatNumber((long) totalRequestForDocsCount) + "</td>" +
                            "<td>" + trafficPercentage + "%</td>" +
                            "</tr>";
                }
            }
        }

        table = table + "</table><br><br>";
        return table;
    }

    /**
     * 生成埋点流量HTML表格（通用方法）
     */
    public static String generateBurialTrafficHtml(List<BurialDTO> burialList, String tableName, String burialItemName) {
        String table = "";
        table = table + "<p>%s</p>".formatted(tableName);

        if (CollectionUtil.isEmpty(burialList)) {
            return table;
        }

        // 组装html
        StringBuilder tableHtml = new StringBuilder();

        tableHtml.append("<table>")
                .append("<tr>")
                .append("<th>日期</th>")
                .append("<th>产品方案</th>")
                .append("<th>").append(burialItemName).append("</th>")
                .append("<th>总数</th>")
                .append("<th>有结果</th>")
                .append("<th>无结果</th>")
                .append("<th>有结果百分比</th>")
                .append("<th>流量占比</th>")
                .append("</tr>");

        for (BurialDTO dto : burialList) {
            String date = dto.getDate();
            List<BurialDTO.Product> products = dto.getDetails() != null ? dto.getDetails() : Collections.emptyList();

            // 计算当前日期的总行数（所有产品的埋点项数量之和）
            int dateRowspan = products.stream()
                    .mapToInt(p -> (p.getDetails() != null && !p.getDetails().isEmpty()) ? p.getDetails().size() : 0)
                    .sum();

            if (dateRowspan == 0) continue; // 跳过无数据的日期

            boolean isDateRowspanAdded = false;

            for (BurialDTO.Product product : products) {
                String productName = product.getProduct();
                List<BurialDTO.Burial> burials = product.getDetails() != null ? product.getDetails() : Collections.emptyList();
                int productRowspan = burials.size();

                if (productRowspan == 0) continue; // 跳过无埋点的产品

                boolean isProductRowspanAdded = false;

                for (BurialDTO.Burial burial : burials) {
                    tableHtml.append("<tr>");

                    // 添加日期列（仅第一次出现时添加）
                    if (!isDateRowspanAdded) {
                        tableHtml.append("<td rowspan='").append(dateRowspan).append("'>").append(date).append("</td>");
                        isDateRowspanAdded = true;
                    }

                    // 添加产品列（仅第一次出现时添加）
                    if (!isProductRowspanAdded) {
                        tableHtml.append("<td rowspan='").append(productRowspan).append("'>").append(productName).append("</td>");
                        isProductRowspanAdded = true;
                    }

                    // 添加埋点数据列
                    tableHtml.append("<td>").append(burial.getBurialItem()).append("</td>")
                            .append("<td>").append(burial.getRequestCount()).append("</td>")
                            .append("<td>").append(burial.getHasResultCount()).append("</td>")
                            .append("<td>").append(burial.getNoResultCount()).append("</td>")
                            .append("<td>").append(burial.getHasResultPercentage()).append("%</td>")
                            .append("<td>").append(burial.getTrafficPercentage()).append("%</td>");

                    tableHtml.append("</tr>");
                }
            }
        }

        tableHtml.append("</table><br>");
        return table + tableHtml.toString();
    }

    /**
     * 生成业务应用概览HTML表格
     */
    public static String generateAppMailHtml(Map<String, List<MailStatisticsDTO>> resultMap,
                                             Map<String, DefaultCategoryDataset> chartDatasets, String tableName) {
        String table = "";
        table = table + "<p>%s</p>".formatted(tableName);
        table = table + "按照【业务应用】维度，仅统计线上环境的请求情况，包括请求总数、有/无结果数和有结果百分比等。<br><br>";
        table = table + "<table>" +
                "    <tr>" +
                "        <th >日期</th>" +
                "        <th >业务应用</th>" +
                "        <th >请求总数</th>" +
                "        <th >有结果</th>" +
                "        <th >无结果</th>" +
                "        <th >有结果百分比</th>" +
                "    </tr>";

        try {
            // appId请求数量趋势图
            DefaultCategoryDataset requestDataset = new DefaultCategoryDataset();
            DefaultCategoryDataset successDataset = new DefaultCategoryDataset();

            for (Map.Entry<String, List<MailStatisticsDTO>> entry : resultMap.entrySet()) {
                table = table + "<tr>";
                int rowSize = entry.getValue().size();
                table = table + "<td rowspan= " + (rowSize == 0 ? 1 : rowSize) + ">" + entry.getKey() + "</td>";

                if (rowSize == 0) {
                    table = table +
                            "<td style=\"text-align: left;\">" + "--" + "</td>" +
                            "<td>" + "--" + "</td>" +
                            "<td>" + "--" + "</td>" +
                            "<td>" + "--" + "</td>" +
                            "</tr>";
                    continue;
                }

                for (MailStatisticsDTO dto : entry.getValue()) {
                    table = table +
                            "<td style=\"text-align: left;\">" + dto.getRemark() + "</td>" +
                            "<td>" + DataCalculateUtil.getFormatNumber(dto.getRequestCount()) + "</td>" +
                            "<td>" + DataCalculateUtil.getFormatNumber(dto.getHasResult()) + "</td>" +
                            "<td>" + DataCalculateUtil.getFormatNumber(dto.getNoResult()) + "</td>" +
                            "<td>" + dto.getHasResultPercentage() + "%</td>" +
                            "</tr>";

                    requestDataset.addValue(dto.getRequestCount(), dto.getRemark(), entry.getKey());
                    successDataset.addValue(dto.getHasResultPercentage(), dto.getRemark(), entry.getKey());
                }
            }

            chartDatasets.put("业务应用请求有结果率", successDataset);
            chartDatasets.put("业务应用请求趋势", requestDataset);

            table = table + "</table>";
            table = table + "<br><br>";

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return table;
    }

    /**
     * 生成产品方案概览HTML表格
     */
    public static String generateProductMailHtml(Map<String, Map<String, Map<String, List<FlowAnalysisMailDTO>>>> htmlMap,
                                                 Map<Long, GeneratedFlow> flowMap,
                                                 Map<Long, GeneratedFlowVersion> flowVersionMap,
                                                 String tableName) {
        String table = "";
        table = table + "<p>%s</p>".formatted(tableName);
        table = table + "按照应用绑定的【产品方案】维度，仅统计线上环境的请求情况，包括请求总数、有/无结果数和有结果百分比等。<br><br>";

        table = table + "<table>" +
                "    <tr>" +
                "        <th >日期</th>" +
                "        <th >应用名称</th>" +
                "        <th >产品方案</th>" +
                "        <th >方案版本</th>" +
                "        <th >请求总数</th>" +
                "        <th >有结果</th>" +
                "        <th >无结果</th>" +
                "        <th >有结果百分比</th>" +
                "    </tr>";

        if (htmlMap.size() > 0) {
            // 使用TreeMap并自定义比较器来降序排序
            Map<String, Map<String, Map<String, List<FlowAnalysisMailDTO>>>> sortedMapHtml = new TreeMap<>(Comparator.reverseOrder());
            sortedMapHtml.putAll(htmlMap);

            for (Map.Entry<String, Map<String, Map<String, List<FlowAnalysisMailDTO>>>> dateEntry : sortedMapHtml.entrySet()) {
                String date = dateEntry.getKey();
                Map<String, Map<String, List<FlowAnalysisMailDTO>>> appMap = dateEntry.getValue();
                int dateRowSpan = appMap.values().stream().flatMapToInt(productMap -> productMap.values().stream().mapToInt(List::size)).sum();

                int currentDateRowSpan = dateRowSpan; // 用于跟踪当前日期行的rowspan
                for (Map.Entry<String, Map<String, List<FlowAnalysisMailDTO>>> appEntry : appMap.entrySet()) {
                    String appName = appEntry.getKey();
                    Map<String, List<FlowAnalysisMailDTO>> productMap = appEntry.getValue();
                    int appRowSpan = productMap.values().stream().mapToInt(List::size).sum();

                    int currentAppRowSpan = appRowSpan; // 用于跟踪当前应用名称行的rowspan
                    for (Map.Entry<String, List<FlowAnalysisMailDTO>> productEntry : productMap.entrySet()) {
                        String productCode = productEntry.getKey();
                        List<FlowAnalysisMailDTO> flowAnalysisList = productEntry.getValue();

                        int productRowSpan = flowAnalysisList.size();

                        CollectionUtil.sort(flowAnalysisList, (o1, o2) -> o2.getTrafficPercentage().compareTo(o1.getTrafficPercentage()));

                        for (FlowAnalysisMailDTO flowAnalysis : flowAnalysisList) {
                            // 添加日期行
                            if (currentDateRowSpan == dateRowSpan) {
                                table += "<tr><td rowspan=\"" + dateRowSpan + "\">" + date + "</td>";
                                currentDateRowSpan -= productRowSpan;
                            } else {
                                table += "<tr>";
                            }

                            // 添加应用名称行
                            if (currentAppRowSpan == appRowSpan) {
                                table += "<td style=\"text-align: left;\" rowspan=\"" + appRowSpan + "\">" + appName + "</td>";
                                currentAppRowSpan -= productRowSpan;
                            }

                            // 添加产品方案行
                            if (flowAnalysisList.indexOf(flowAnalysis) == 0) {
                                GeneratedFlow flow = flowMap.get(Long.valueOf(productCode));
                                String flowName = flow != null ? flow.getName() + "(" + flow.getCode() + ")" : productCode;
                                table += "<td style=\"text-align: left;\"  rowspan=\"" + productRowSpan + "\">" + flowName + "</td>";
                            }

                            // 添加方案版本、请求总数等数据
                            GeneratedFlowVersion flowVersion = flowVersionMap.get(Long.valueOf(flowAnalysis.getBizId()));
                            String versionName = flowVersion != null ?
                                flowVersion.getName() + "(V" + String.format("%03d", flowVersion.getVersion()) + ")" :
                                flowAnalysis.getBizId();

                            table += "<td style=\"text-align: left;\">" + versionName + "</td>" +
                                    "<td>" + DataCalculateUtil.getFormatNumber(flowAnalysis.getRequestCount()) + "</td>" +
                                    "<td>" + DataCalculateUtil.getFormatNumber(flowAnalysis.getHasResult()) + "</td>" +
                                    "<td>" + DataCalculateUtil.getFormatNumber(flowAnalysis.getNoResult()) + "</td>" +
                                    "<td>" + flowAnalysis.getHasResultPercentage() + "%</td>" +
                                    "</tr>";
                        }
                    }
                }
            }
        }

        table = table + "</table>";
        table = table + "<br><br>";

        return table;
    }
}
