# Server and Application Config
server.port=8095
spring.application.name=support-manager
IP=127.0.0.1
SKYNET_ACTION_ID=lynxiao_platform
PORT=${server.port}

#------------------------------------------------------------------------------------------------------
# Service Discovery Config (TLB)
skynet.tlb.enabled=true
skynet.tlb.endpoints=${IP}:33001
spring.cloud.discovery.enabled=${skynet.tlb.enabled}
spring.cloud.tlb.discovery.enabled=${skynet.tlb.enabled}
spring.cloud.tlb.discovery.register=${skynet.tlb.enabled}
spring.cloud.tlb.discovery.service-port=0
spring.cloud.tlb.discovery.service-host=${IP}
spring.cloud.tlb.discovery.service-name=${spring.application.name}
spring.cloud.tlb.discovery.service-id=${SKYNET_ACTION_ID}_${IP}_${PORT}
spring.cloud.tlb.discovery.max-lic=64
spring.cloud.tlb.discovery.default-service-tag-selector=A

#------------------------------------------------------------------------------------------------------
# Spring Framework Config
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
spring.threads.virtual.enabled=true
spring.liquibase.enabled=false
spring.liquibase.change-log=classpath:/liquibase/master.xml
spring.data.mongodb.auto-index-creation=false

#------------------------------------------------------------------------------------------------------
# Monitoring and Metrics Config
management.metrics.tags.application=${spring.application.name}
management.endpoints.web.exposure.include=*
management.endpoint.env.show-values=ALWAYS
management.endpoint.metrics.access=read-only
management.metrics.enable.all=true
management.metrics.enable.jvm=true

#------------------------------------------------------------------------------------------------------
# Security Config
skynet.security.enabled=true
skynet.security.sign-auth.enabled=false
skynet.security.sign-auth.app.lynxiao=S47AC10B-K8CC-Y372-N567-TE02B2C3D479
skynet.security.base-auth.enabled=false
skynet.security.base-auth.user.name=admin
skynet.security.base-auth.user.password=skynet2230
spring.security.user.name=admin
spring.security.user.password=skynet2230

#------------------------------------------------------------------------------------------------------
# Pandora Ogma Config
skynet.pandora.ogma.enabled=true
skynet.pandora.ogma.api-key=lynxiao
skynet.pandora.ogma.api-secret=${skynet.security.sign-auth.app.lynxiao}
skynet.pandora.ogma.timeout=60s
skynet.pandora.ogma.default-service-tag-selector=${spring.cloud.tlb.discovery.default-service-tag-selector}
skynet.security.auth-client.api-key=${skynet.pandora.ogma.api-key}
skynet.security.auth-client.api-secret=${skynet.pandora.ogma.api-secret}
skynet.security.auth-client.timeout=${skynet.pandora.ogma.timeout}

#------------------------------------------------------------------------------------------------------
# es client 开关 默认全开
lynxiao.log.hf.enabled=true
lynxiao.log.sh.enabled=true
lynxiao.log.dx.enabled=true

#------------------------------------------------------------------------------------------------------
# Logging Config
logging.level.org.apache.kafka=ERROR
logging.level.org.apache.kafka.clients=ERROR
logging.level.org.elasticsearch.client.RestClient=ERROR