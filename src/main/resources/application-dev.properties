# support
cluster.mysql.support.url=********************************************************************************************************************************************
cluster.mysql.support.username=u_lynxiao
cluster.mysql.support.password=I4ZTw228sy8IHpL6oHjr
cluster.mysql.support.type=com.zaxxer.hikari.HikariDataSource

# portal
# 生产
cluster.mysql.portal.url=******************************************************************************************************************************************************************
cluster.mysql.portal.username=u_lynxiao_read
cluster.mysql.portal.password=GhLFpVB5B8LGt98oQcw0

# 研发
#cluster.mysql.portal.url=*******************************************************************************************************************************************
#cluster.mysql.portal.username=u_lynxiao
#cluster.mysql.portal.password=I4ZTw228sy8IHpL6oHjr
cluster.mysql.portal.type=com.zaxxer.hikari.HikariDataSource

#mongo
spring.data.mongodb.uri=***********************************************************************************************************************************************************



# mail
spring.mail.host=mail.iflymail.com.cn
spring.mail.username=<EMAIL>
spring.mail.password=HY4sF2Gcjs4rCcSY



lynxiao.flow.analysis.mail.all.to=<EMAIL>
lynxiao.flow.analysis.mail.simple.to=<EMAIL>
lynxiao.flow.analysis.mail.cc=

# ???????????appId
lynxiao.flow.analysis.health.scene.appIds=cc501f15,b4b7d678
lynxiao.flow.analysis.health.scene.traffic=3

lynxiao.flow.analysis.biz-equal-id-field=sid
lynxiao.flow.analysis.env=prod


skynet.tlb.endpoints=**************:30132
spring.cloud.tlb.discovery.default-service-tag-selector=A
spring.cloud.tlb.discovery.register=false



#----------------------------------------????????----------------------------------
# hf-ES
lynxiao.log.hf.elasticsearch.uris=es-EOECHZYCLI.es.zone-hfyc-1.dbaas.private:25245
lynxiao.log.hf.elasticsearch.username=esadmin
lynxiao.log.hf.elasticsearch.password=QTd+DB2qTA9+ApMK

## sh-ES
lynxiao.log.sh.enabled=false
#lynxiao.log.sh.elasticsearch.uris=************:19200
#lynxiao.log.sh.elasticsearch.username=only_read
#lynxiao.log.sh.elasticsearch.password=1qaz@wsx
#
## dx-ES
lynxiao.log.dx.enabled=false
#lynxiao.log.dx.elasticsearch.uris=elk-es.elk-bj02-zjeekv.svc.bjb.ipaas.cn:9200
#lynxiao.log.dx.elasticsearch.username=elastic
#lynxiao.log.dx.elasticsearch.password=111qqq...

logging.level.com.iflytek.lynxiao.support.service=TRACE


